-- Test script for trustee database functions

-- Test create_trustee_safely function
SELECT create_trustee_safely(
  '00000000-0000-0000-0000-000000000001', -- p_user_id (example UUID)
  '<EMAIL>', -- p_trustee_email
  'Test', -- p_first_name
  'User', -- p_last_name
  ARRAY['assets', 'vault', 'contacts'], -- p_permissions
  '<EMAIL>', -- p_inviter_email
  'Owner', -- p_inviter_first_name
  'User' -- p_inviter_last_name
);

-- Test update_trustee_invitation function
-- Note: Replace the UUID with an actual trustee ID from your database
SELECT update_trustee_invitation(
  '00000000-0000-0000-0000-000000000002', -- p_trustee_id (example UUID)
  NOW() -- p_current_time
);

-- Test activate_trustee_safely function
-- Note: Replace the UUIDs with actual values from your database
SELECT activate_trustee_safely(
  '00000000-0000-0000-0000-000000000002', -- p_trustee_id (example UUID)
  '00000000-0000-0000-0000-000000000003' -- p_user_id (example UUID)
);
