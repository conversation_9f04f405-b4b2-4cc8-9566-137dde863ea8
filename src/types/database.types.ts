export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      assets: {
        Row: {
          account_details: string | null
          acquisition_date: string | null
          category: string
          created_at: string
          currency: string | null
          description: string | null
          id: string
          image_url: string | null
          location: string | null
          name: string
          notes: string | null
          type: string
          updated_at: string
          user_id: string
          value: number | null
        }
        Insert: {
          account_details?: string | null
          acquisition_date?: string | null
          category: string
          created_at?: string
          currency?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          location?: string | null
          name: string
          notes?: string | null
          type: string
          updated_at?: string
          user_id: string
          value?: number | null
        }
        Update: {
          account_details?: string | null
          acquisition_date?: string | null
          category?: string
          created_at?: string
          currency?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          location?: string | null
          name?: string
          notes?: string | null
          type?: string
          updated_at?: string
          user_id?: string
          value?: number | null
        }
        Relationships: []
      }
      auth_users_mapping: {
        Row: {
          auth_user_id: string
          created_at: string
          custom_user_id: string
          id: string
        }
        Insert: {
          auth_user_id: string
          created_at?: string
          custom_user_id: string
          id?: string
        }
        Update: {
          auth_user_id?: string
          created_at?: string
          custom_user_id?: string
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "auth_users_mapping_custom_user_id_fkey"
            columns: ["custom_user_id"]
            isOneToOne: true
            referencedRelation: "custom_users"
            referencedColumns: ["id"]
          },
        ]
      }
      contacts: {
        Row: {
          address: string | null
          country_code: string | null
          created_at: string
          email: string | null
          first_name: string
          has_personal_message: boolean | null
          id: string
          last_name: string
          notes: string | null
          personal_message: string | null
          phone: string | null
          relationship: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          address?: string | null
          country_code?: string | null
          created_at?: string
          email?: string | null
          first_name: string
          has_personal_message?: boolean | null
          id?: string
          last_name: string
          notes?: string | null
          personal_message?: string | null
          phone?: string | null
          relationship?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          address?: string | null
          country_code?: string | null
          created_at?: string
          email?: string | null
          first_name?: string
          has_personal_message?: boolean | null
          id?: string
          last_name?: string
          notes?: string | null
          personal_message?: string | null
          phone?: string | null
          relationship?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "contacts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contacts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_subscription_limits"
            referencedColumns: ["id"]
          },
        ]
      }
      custom_users: {
        Row: {
          created_at: string | null
          email: string
          email_verified: boolean | null
          first_name: string
          id: string
          is_recipient: boolean | null
          last_name: string
          password_hash: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          email_verified?: boolean | null
          first_name: string
          id?: string
          is_recipient?: boolean | null
          last_name: string
          password_hash: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          email_verified?: boolean | null
          first_name?: string
          id?: string
          is_recipient?: boolean | null
          last_name?: string
          password_hash?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      death_notifications: {
        Row: {
          created_at: string
          death_certificate: string | null
          id: string
          reported_at: string
          reported_by: string
          status: string
          updated_at: string
          user_id: string
          verification_details: string | null
          verification_method: string | null
          verified_at: string | null
        }
        Insert: {
          created_at?: string
          death_certificate?: string | null
          id?: string
          reported_at: string
          reported_by: string
          status: string
          updated_at?: string
          user_id: string
          verification_details?: string | null
          verification_method?: string | null
          verified_at?: string | null
        }
        Update: {
          created_at?: string
          death_certificate?: string | null
          id?: string
          reported_at?: string
          reported_by?: string
          status?: string
          updated_at?: string
          user_id?: string
          verification_details?: string | null
          verification_method?: string | null
          verified_at?: string | null
        }
        Relationships: []
      }
      documents: {
        Row: {
          category: string
          created_at: string
          description: string | null
          encryption_key: string
          file_name: string
          file_size: number | null
          file_type: string | null
          id: string
          name: string
          updated_at: string
          user_id: string
        }
        Insert: {
          category: string
          created_at?: string
          description?: string | null
          encryption_key: string
          file_name: string
          file_size?: number | null
          file_type?: string | null
          id?: string
          name: string
          updated_at?: string
          user_id: string
        }
        Update: {
          category?: string
          created_at?: string
          description?: string | null
          encryption_key?: string
          file_name?: string
          file_size?: number | null
          file_type?: string | null
          id?: string
          name?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      invitation_sessions: {
        Row: {
          created_at: string
          email: string
          expires_at: string
          id: string
          session_token: string
          terms_accepted: boolean | null
          trustee_id: string
          used: boolean | null
        }
        Insert: {
          created_at?: string
          email: string
          expires_at: string
          id?: string
          session_token: string
          terms_accepted?: boolean | null
          trustee_id: string
          used?: boolean | null
        }
        Update: {
          created_at?: string
          email?: string
          expires_at?: string
          id?: string
          session_token?: string
          terms_accepted?: boolean | null
          trustee_id?: string
          used?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "invitation_sessions_trustee_id_fkey"
            columns: ["trustee_id"]
            isOneToOne: false
            referencedRelation: "trustees"
            referencedColumns: ["id"]
          },
        ]
      }
      last_wishes: {
        Row: {
          burial_option: string | null
          burial_wishes: string | null
          created_at: string
          funeral_wishes: string | null
          has_pets: boolean
          id: string
          is_organ_donor: boolean
          organ_donor_country: string | null
          organ_donor_state: string | null
          other_wishes: string | null
          personal_messages: string | null
          pet_care_instructions: string | null
          show_personal_messages: boolean | null
          updated_at: string
          user_id: string
        }
        Insert: {
          burial_option?: string | null
          burial_wishes?: string | null
          created_at?: string
          funeral_wishes?: string | null
          has_pets?: boolean
          id?: string
          is_organ_donor?: boolean
          organ_donor_country?: string | null
          organ_donor_state?: string | null
          other_wishes?: string | null
          personal_messages?: string | null
          pet_care_instructions?: string | null
          show_personal_messages?: boolean | null
          updated_at?: string
          user_id: string
        }
        Update: {
          burial_option?: string | null
          burial_wishes?: string | null
          created_at?: string
          funeral_wishes?: string | null
          has_pets?: boolean
          id?: string
          is_organ_donor?: boolean
          organ_donor_country?: string | null
          organ_donor_state?: string | null
          other_wishes?: string | null
          personal_messages?: string | null
          pet_care_instructions?: string | null
          show_personal_messages?: boolean | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "last_wishes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "custom_users"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          email: string | null
          first_name: string | null
          id: string
          last_name: string | null
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          subscription_end_date: string | null
          subscription_start_date: string | null
          subscription_status: string
          subscription_tier: string
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          first_name?: string | null
          id: string
          last_name?: string | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string
          subscription_tier?: string
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          first_name?: string | null
          id?: string
          last_name?: string | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string
          subscription_tier?: string
          updated_at?: string
        }
        Relationships: []
      }
      service_sunset: {
        Row: {
          account_number: string | null
          auto_renewal: boolean
          cancellation_instructions: string | null
          category: string
          contact_info: string | null
          cost_per_period: number | null
          created_at: string
          custom_category: string | null
          description: string | null
          id: string
          name: string
          period: string | null
          priority: string
          renewal_date: string | null
          updated_at: string
          user_id: string
          website: string | null
        }
        Insert: {
          account_number?: string | null
          auto_renewal?: boolean
          cancellation_instructions?: string | null
          category: string
          contact_info?: string | null
          cost_per_period?: number | null
          created_at?: string
          custom_category?: string | null
          description?: string | null
          id?: string
          name: string
          period?: string | null
          priority: string
          renewal_date?: string | null
          updated_at?: string
          user_id: string
          website?: string | null
        }
        Update: {
          account_number?: string | null
          auto_renewal?: boolean
          cancellation_instructions?: string | null
          category?: string
          contact_info?: string | null
          created_at?: string
          custom_category?: string | null
          description?: string | null
          id?: string
          name?: string
          renewal_date?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      time_capsule_media: {
        Row: {
          capsule_id: string
          created_at: string
          file_name: string
          file_path: string
          file_size: number
          file_type: string
          id: string
        }
        Insert: {
          capsule_id: string
          created_at?: string
          file_name: string
          file_path: string
          file_size: number
          file_type: string
          id?: string
        }
        Update: {
          capsule_id?: string
          created_at?: string
          file_name?: string
          file_path?: string
          file_size?: number
          file_type?: string
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "time_capsule_media_capsule_id_fkey"
            columns: ["capsule_id"]
            isOneToOne: false
            referencedRelation: "time_capsules"
            referencedColumns: ["id"]
          },
        ]
      }
      time_capsules: {
        Row: {
          access_code: string | null
          created_at: string
          delivery_date: string
          delivery_hour: number | null
          id: string
          message: string | null
          recipient_email: string
          recipient_first_name: string | null
          recipient_last_name: string | null
          recipient_name: string | null
          sender_name: string | null
          status: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          access_code?: string | null
          created_at?: string
          delivery_date: string
          delivery_hour?: number | null
          id?: string
          message?: string | null
          recipient_email: string
          recipient_first_name?: string | null
          recipient_last_name?: string | null
          recipient_name?: string | null
          sender_name?: string | null
          status: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          access_code?: string | null
          created_at?: string
          delivery_date?: string
          delivery_hour?: number | null
          id?: string
          message?: string | null
          recipient_email?: string
          recipient_first_name?: string | null
          recipient_last_name?: string | null
          recipient_name?: string | null
          sender_name?: string | null
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "time_capsules_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "custom_users"
            referencedColumns: ["id"]
          },
        ]
      }
      trustee_operations_log: {
        Row: {
          created_at: string | null
          details: Json | null
          id: string
          operation: string
          performed_by: string | null
          trustee_id: string
        }
        Insert: {
          created_at?: string | null
          details?: Json | null
          id?: string
          operation: string
          performed_by?: string | null
          trustee_id: string
        }
        Update: {
          created_at?: string | null
          details?: Json | null
          id?: string
          operation?: string
          performed_by?: string | null
          trustee_id?: string
        }
        Relationships: []
      }
      trustee_tokens: {
        Row: {
          created_at: string
          expires_at: string
          id: string
          token: string
          trustee_id: string
          used: boolean | null
        }
        Insert: {
          created_at?: string
          expires_at: string
          id?: string
          token: string
          trustee_id: string
          used?: boolean | null
        }
        Update: {
          created_at?: string
          expires_at?: string
          id?: string
          token?: string
          trustee_id?: string
          used?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "trustee_tokens_trustee_id_fkey"
            columns: ["trustee_id"]
            isOneToOne: false
            referencedRelation: "trustees"
            referencedColumns: ["id"]
          },
        ]
      }
      trustees: {
        Row: {
          created_at: string
          first_name: string
          id: string
          invitation_accepted_at: string | null
          invitation_sent_at: string | null
          inviter_email: string | null
          inviter_first_name: string | null
          inviter_last_name: string | null
          last_name: string
          permissions: string[]
          relationship: string | null
          status: string
          terms_accepted: boolean | null
          terms_accepted_at: string | null
          trustee_email: string
          trustee_user_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          first_name: string
          id?: string
          invitation_accepted_at?: string | null
          invitation_sent_at?: string | null
          inviter_email?: string | null
          inviter_first_name?: string | null
          inviter_last_name?: string | null
          last_name: string
          permissions: string[]
          relationship?: string | null
          status: string
          terms_accepted?: boolean | null
          terms_accepted_at?: string | null
          trustee_email: string
          trustee_user_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          first_name?: string
          id?: string
          invitation_accepted_at?: string | null
          invitation_sent_at?: string | null
          inviter_email?: string | null
          inviter_first_name?: string | null
          inviter_last_name?: string | null
          last_name?: string
          permissions?: string[]
          relationship?: string | null
          status?: string
          terms_accepted?: boolean | null
          terms_accepted_at?: string | null
          trustee_email?: string
          trustee_user_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_profiles: {
        Row: {
          created_at: string | null
          email: string
          first_name: string
          id: string
          last_name: string
        }
        Insert: {
          created_at?: string | null
          email: string
          first_name: string
          id: string
          last_name: string
        }
        Update: {
          created_at?: string | null
          email?: string
          first_name?: string
          id?: string
          last_name?: string
        }
        Relationships: []
      }
      user_sessions: {
        Row: {
          created_at: string | null
          expires_at: string
          id: string
          session_token: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          expires_at: string
          id?: string
          session_token: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          expires_at?: string
          id?: string
          session_token?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "custom_users"
            referencedColumns: ["id"]
          },
        ]
      }
      vault_documents: {
        Row: {
          category: string
          created_at: string
          description: string | null
          encryption_key: string | null
          file_name: string | null
          file_path: string
          file_size: number | null
          file_type: string | null
          id: string
          is_encrypted: boolean
          name: string
          updated_at: string
          user_id: string
        }
        Insert: {
          category: string
          created_at?: string
          description?: string | null
          encryption_key?: string | null
          file_name?: string | null
          file_path: string
          file_size?: number | null
          file_type?: string | null
          id?: string
          is_encrypted?: boolean
          name: string
          updated_at?: string
          user_id: string
        }
        Update: {
          category?: string
          created_at?: string
          description?: string | null
          encryption_key?: string | null
          file_name?: string | null
          file_path?: string
          file_size?: number | null
          file_type?: string | null
          id?: string
          is_encrypted?: boolean
          name?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      verification_codes: {
        Row: {
          code: string
          created_at: string | null
          email: string
          expires_at: string
          id: string
          used: boolean | null
        }
        Insert: {
          code: string
          created_at?: string | null
          email: string
          expires_at: string
          id?: string
          used?: boolean | null
        }
        Update: {
          code?: string
          created_at?: string | null
          email?: string
          expires_at?: string
          id?: string
          used?: boolean | null
        }
        Relationships: []
      }
      will_progress: {
        Row: {
          assets_completed: boolean
          basic_info_completed: boolean
          created_at: string
          executor_completed: boolean
          final_review_completed: boolean
          id: string
          progress_percentage: number
          updated_at: string
          user_id: string
        }
        Insert: {
          assets_completed?: boolean
          basic_info_completed?: boolean
          created_at?: string
          executor_completed?: boolean
          final_review_completed?: boolean
          id?: string
          progress_percentage?: number
          updated_at?: string
          user_id: string
        }
        Update: {
          assets_completed?: boolean
          basic_info_completed?: boolean
          created_at?: string
          executor_completed?: boolean
          final_review_completed?: boolean
          id?: string
          progress_percentage?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      user_subscription_limits: {
        Row: {
          email: string | null
          id: string | null
          max_assets: string | null
          max_contacts: string | null
          max_service_sunset: string | null
          max_time_capsules: number | null
          max_trustees: number | null
          max_vault_storage_bytes: number | null
          subscription_status: string | null
          subscription_tier: string | null
        }
        Insert: {
          email?: string | null
          id?: string | null
          max_assets?: never
          max_contacts?: never
          max_service_sunset?: never
          max_time_capsules?: never
          max_trustees?: never
          max_vault_storage_bytes?: never
          subscription_status?: string | null
          subscription_tier?: string | null
        }
        Update: {
          email?: string | null
          id?: string | null
          max_assets?: never
          max_contacts?: never
          max_service_sunset?: never
          max_time_capsules?: never
          max_trustees?: never
          max_vault_storage_bytes?: never
          subscription_status?: string | null
          subscription_tier?: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      activate_simple_trustee: {
        Args: { p_trustee_id: string; p_user_id: string }
        Returns: Json
      }
      activate_trustee: {
        Args: { p_trustee_id: string; p_user_id: string }
        Returns: undefined
      }
      activate_trustee_safely: {
        Args: { p_trustee_id: string; p_user_id: string }
        Returns: Json
      }
      cleanup_expired_tokens: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      create_invitation_session: {
        Args: { p_trustee_id: string; p_email: string; p_expiry_hours?: number }
        Returns: Json
      }
      create_simple_trustee: {
        Args: {
          p_user_id: string
          p_trustee_email: string
          p_first_name: string
          p_last_name: string
          p_permissions?: string[]
        }
        Returns: Json
      }
      create_simple_trustee_no_constraints: {
        Args: {
          p_user_id: string
          p_trustee_email: string
          p_first_name: string
          p_last_name: string
          p_permissions?: string[]
        }
        Returns: Json
      }
      create_trustee: {
        Args: {
          p_user_id: string
          p_trustee_email: string
          p_trustee_user_id: string
          p_first_name: string
          p_last_name: string
          p_relationship: string
          p_permissions: string[]
        }
        Returns: {
          created_at: string
          first_name: string
          id: string
          invitation_accepted_at: string | null
          invitation_sent_at: string | null
          inviter_email: string | null
          inviter_first_name: string | null
          inviter_last_name: string | null
          last_name: string
          permissions: string[]
          relationship: string | null
          status: string
          terms_accepted: boolean | null
          terms_accepted_at: string | null
          trustee_email: string
          trustee_user_id: string | null
          updated_at: string
          user_id: string
        }[]
      }
      create_trustee_direct: {
        Args: {
          p_custom_user_id: string
          p_trustee_email: string
          p_first_name: string
          p_last_name: string
          p_relationship?: string
          p_permissions?: string[]
        }
        Returns: string
      }
      create_trustee_no_constraints: {
        Args: {
          p_custom_user_id: string
          p_trustee_email: string
          p_first_name: string
          p_last_name: string
          p_relationship?: string
          p_permissions?: string[]
        }
        Returns: Json
      }
      create_trustee_safely: {
        Args: {
          p_user_id: string
          p_trustee_email: string
          p_first_name: string
          p_last_name: string
          p_permissions: string[]
          p_inviter_email: string
          p_inviter_first_name: string
          p_inviter_last_name: string
        }
        Returns: Json
      }
      create_trustee_token: {
        Args: { p_trustee_id: string; p_expiry_days?: number }
        Returns: Json
      }
      create_trustee_with_mapping: {
        Args: {
          p_custom_user_id: string
          p_trustee_email: string
          p_first_name: string
          p_last_name: string
          p_relationship?: string
          p_permissions?: string[]
        }
        Returns: Json
      }
      direct_create_trustee: {
        Args: {
          p_custom_user_id: string
          p_trustee_email: string
          p_first_name: string
          p_last_name: string
          p_relationship?: string
          p_permissions?: string[]
        }
        Returns: Json
      }
      direct_update_trustee_invitation: {
        Args: { p_trustee_id: string; p_current_time: string }
        Returns: undefined
      }
      ensure_auth_user_mapping: {
        Args: { p_custom_user_id: string }
        Returns: string
      }
      exec_sql: {
        Args: { sql: string }
        Returns: undefined
      }
      find_pending_trustee_invitations: {
        Args: { p_email: string }
        Returns: Json
      }
      fix_auth_mapping: {
        Args: { p_custom_user_id: string }
        Returns: string
      }
      fix_auth_user_mapping: {
        Args: { p_custom_user_id: string }
        Returns: string
      }
      fix_existing_trustees: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      fix_missing_trustee_inviter_info: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      fix_trustees_foreign_key: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      fix_trustees_foreign_keys: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      fix_user_auth_mapping: {
        Args: { p_user_id: string }
        Returns: Json
      }
      force_update_trustee: {
        Args: {
          p_trustee_id: string
          p_user_id: string
          p_current_time: string
        }
        Returns: Json
      }
      generate_secure_token: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_or_create_auth_user_mapping: {
        Args: { p_custom_user_id: string }
        Returns: string
      }
      has_reached_limit: {
        Args: { user_id: string; limit_type: string }
        Returns: boolean
      }
      invalidate_trustee_token: {
        Args: { p_trustee_id: string; p_token: string }
        Returns: boolean
      }
      is_user_a_trustee: {
        Args: { p_user_id: string }
        Returns: boolean
      }
      trustee_activation: {
        Args: {
          p_trustee_id: string
          p_user_id: string
          p_trustee_email?: string
          p_token?: string
        }
        Returns: Json
      }
      update_invitation_session: {
        Args: {
          p_session_token: string
          p_terms_accepted?: boolean
          p_used?: boolean
        }
        Returns: Json
      }
      update_trustee_invitation: {
        Args: { p_trustee_id: string; p_current_time: string }
        Returns: Json
      }
      update_trustee_status: {
        Args: { p_trustee_id: string; p_status: string }
        Returns: Json
      }
      validate_invitation_session: {
        Args: { p_session_token: string }
        Returns: Json
      }
      validate_trustee_activation: {
        Args: {
          p_trustee_id: string
          p_trustee_email?: string
          p_token?: string
        }
        Returns: Json
      }
      validate_trustee_token: {
        Args: { p_trustee_id: string; p_token: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
  | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
    Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
    Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
  ? R
  : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
    DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
    DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R
    }
  ? R
  : never
  : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
  | keyof DefaultSchema["Tables"]
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Insert: infer I
  }
  ? I
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Insert: infer I
  }
  ? I
  : never
  : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
  | keyof DefaultSchema["Tables"]
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Update: infer U
  }
  ? U
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Update: infer U
  }
  ? U
  : never
  : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
  | keyof DefaultSchema["Enums"]
  | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
  : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never

export type Contact = Tables<'contacts'>;
export type LastWishes = Tables<'last_wishes'>;
export type TimeCapsule = Tables<'time_capsules'>;
export type TimeCapsuleMedia = Tables<'time_capsule_media'> & {
  signed_url?: string;
};
export type ServiceSunset = Tables<'service_sunset'>;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
  | keyof DefaultSchema["CompositeTypes"]
  | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
  : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
