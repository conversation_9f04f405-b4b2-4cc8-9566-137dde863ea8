"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { supabase } from '@/lib/supabase-unified';
import { useAuth } from '@/context/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, FileText, Download, Eye, ArrowLeft, Trash2, AlertCircle } from 'lucide-react';
import PageHeading from '@/components/ui/PageHeading';
import { decryptFile } from '@/utils/encryption';
import { toast } from 'sonner';
import { Tables } from '@/types/database.types';
import UploadDocumentDialog from '@/components/Vault/UploadDocumentDialog';

export default function VaultPage() {
  const { user } = useAuth();
  const [documents, setDocuments] = useState<Tables<'documents'>[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Tables<'documents'> | null>(null);
  const [storageUsed, setStorageUsed] = useState<number>(0);
  const [storageLimit, setStorageLimit] = useState<number>(5 * 1024 * 1024 * 1024); // 5GB default

  const documentCategories = [
    { value: 'all', label: 'All Documents' },
    { value: 'financial', label: 'Financial Records' },
    { value: 'legal', label: 'Legal Documents' },
    { value: 'personal', label: 'Personal Documents' },
    { value: 'other', label: 'Other' },
  ];

  useEffect(() => {
    if (user) {
      console.log('User authenticated:', user);
      fetchDocuments();
      calculateStorageUsed();
    } else {
      console.log('No user found');
    }
  }, [user, searchQuery, selectedCategory]);

  const calculateStorageUsed = async () => {
    try {
      // Fetch all documents to calculate total storage used
      const response = await fetch('/api/vault/documents');

      if (!response.ok) {
        throw new Error(`Failed to fetch documents: ${response.statusText}`);
      }

      const data = await response.json();

      // Calculate total size
      const totalSize = data.reduce((total: number, doc: Tables<'documents'>) => {
        return total + (doc.file_size || 0);
      }, 0);

      setStorageUsed(totalSize);
    } catch (error) {
      console.error('Error calculating storage used:', error);
    }
  };

  const fetchDocuments = async () => {
    try {
      setIsLoading(true);

      // Build the API URL with query parameters
      let url = '/api/vault/documents';
      const params = new URLSearchParams();

      if (searchQuery) {
        params.append('search', searchQuery);
      }

      if (selectedCategory && selectedCategory !== 'all') {
        params.append('category', selectedCategory);
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      // Fetch documents from the API
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch documents: ${response.statusText}`);
      }

      const data = await response.json();

      console.log('Fetched documents:', data);

      setDocuments(data || []);
    } catch (error: any) {
      console.error('Error fetching documents:', error);
      toast.error(`Failed to load documents: ${error.message}`);
      setDocuments([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDocument = async (document: Tables<'documents'>) => {
    try {
      setIsProcessing(document.id);

      console.log('Viewing document:', document);

      // Construct file path from user_id and file_name
      let filePath = `${user?.id}/${document.file_name}`;

      console.log('Attempting to download file from path:', filePath);

      // Ensure the file path is properly formatted
      if (filePath.startsWith('/')) {
        filePath = filePath.substring(1);
      }

      const { data, error } = await supabase.storage
        .from('documents')
        .download(filePath);

      if (error) {
        console.error('Storage download error:', error);
        throw new Error(`Failed to download document: ${error.message || JSON.stringify(error)}`);
      }

      if (!data) {
        throw new Error('No data received from storage');
      }

      // Convert Base64 encryption key back to Uint8Array
      if (!document.encryption_key) {
        throw new Error('Document encryption key is missing');
      }

      console.log('Decrypting document with key length:', document.encryption_key.length);

      // Use the base64ToArrayBuffer utility function instead of manual conversion
      // This is safer and handles errors better
      const { base64ToArrayBuffer } = await import('@/utils/encryption');
      const encryptionKey = base64ToArrayBuffer(document.encryption_key);

      // Decrypt the file
      console.log('Decrypting file of size:', data.size);
      const decryptedFile = await decryptFile(data, encryptionKey);

      // Create blob URL and open in new tab
      const blobUrl = URL.createObjectURL(decryptedFile);
      window.open(blobUrl, '_blank');
    } catch (error: any) {
      console.error('Error viewing document:', error);
      toast.error(`Failed to view document: ${error.message || 'Unknown error'}`);
    } finally {
      setIsProcessing(null);
    }
  };

  const handleDownloadDocument = async (document: Tables<'documents'>) => {
    try {
      setIsProcessing(document.id);

      console.log('Downloading document:', document);

      // Construct file path from user_id and file_name
      let filePath = `${user?.id}/${document.file_name}`;

      console.log('Attempting to download file from path:', filePath);

      // Ensure the file path is properly formatted
      if (filePath.startsWith('/')) {
        filePath = filePath.substring(1);
      }

      const { data, error } = await supabase.storage
        .from('documents')
        .download(filePath);

      if (error) {
        console.error('Storage download error:', error);
        throw new Error(`Failed to download document: ${error.message || JSON.stringify(error)}`);
      }

      if (!data) {
        throw new Error('No data received from storage');
      }

      // Convert Base64 encryption key back to Uint8Array
      if (!document.encryption_key) {
        throw new Error('Document encryption key is missing');
      }

      console.log('Decrypting document with key length:', document.encryption_key.length);

      // Use the base64ToArrayBuffer utility function instead of manual conversion
      // This is safer and handles errors better
      const { base64ToArrayBuffer } = await import('@/utils/encryption');
      const encryptionKey = base64ToArrayBuffer(document.encryption_key);

      // Decrypt the file
      console.log('Decrypting file of size:', data.size);
      const decryptedFile = await decryptFile(data, encryptionKey);

      // Create blob URL and force download
      const blobUrl = URL.createObjectURL(decryptedFile);
      const a = window.document.createElement('a');
      a.href = blobUrl;
      a.download = document.name;
      window.document.body.appendChild(a);
      a.click();
      window.document.body.removeChild(a);
    } catch (error: any) {
      console.error('Error downloading document:', error);
      toast.error(`Failed to download document: ${error.message || 'Unknown error'}`);
    } finally {
      setIsProcessing(null);
    }
  };

  const confirmDeleteDocument = (document: Tables<'documents'>) => {
    setSelectedDocument(document);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteDocument = async () => {
    if (!selectedDocument) return;

    try {
      setIsDeleting(selectedDocument.id);

      // Call the API to delete the document
      const response = await fetch(`/api/documents/${selectedDocument.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to delete document: ${response.statusText}`);
      }

      toast.success('Document deleted successfully');

      // Update the documents list by removing the deleted document
      setDocuments(documents.filter(doc => doc.id !== selectedDocument.id));
      setIsDeleteDialogOpen(false);
      setSelectedDocument(null);

      // Recalculate storage used
      calculateStorageUsed();
    } catch (error: any) {
      console.error('Error deleting document:', error);
      toast.error(`Failed to delete document: ${error.message || 'Unknown error'}`);
    } finally {
      setIsDeleting(null);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Button variant="outline" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <PageHeading
        title="Digital Vault"
        description="Securely store and manage your important documents."
        actions={
          <div className="flex flex-col md:flex-row items-end md:items-center space-y-2 md:space-y-0 md:space-x-2">
            <div className="text-sm text-gray-500 mr-2">
              <div className="flex items-center">
                <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full"
                    style={{ width: `${Math.min(100, (storageUsed / storageLimit) * 100)}%` }}
                  ></div>
                </div>
                <span>
                  {(storageUsed / (1024 * 1024)).toFixed(2)} MB / {(storageLimit / (1024 * 1024 * 1024)).toFixed(0)} GB
                </span>
              </div>
            </div>
            <UploadDocumentDialog onDocumentUploaded={() => {
              fetchDocuments();
              calculateStorageUsed();
            }} />
          </div>
        }
      />

      <div className="mb-6 flex items-center space-x-4">
        <div className="relative w-full md:w-1/2">
          <Input
            type="text"
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pr-10"
          />
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
        </div>

        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            {documentCategories.map((category) => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {isLoading ? (
        <div className="text-center py-10">Loading documents...</div>
      ) : documents.length === 0 ? (
        <div className="text-center py-10">No documents found.</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {documents.map((document) => (
            <Card key={document.id}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-gray-500" />
                  {document.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-500">Category: {document.category}</p>
                <div className="mt-4 flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDocument(document)}
                    disabled={isProcessing === document.id || isDeleting === document.id}
                  >
                    {isProcessing === document.id ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Viewing...
                      </>
                    ) : (
                      <>
                        <Eye className="mr-2 h-4 w-4" />
                        View
                      </>
                    )}
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => handleDownloadDocument(document)}
                    disabled={isProcessing === document.id || isDeleting === document.id}
                  >
                    {isProcessing === document.id ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Downloading...
                      </>
                    ) : (
                      <>
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </>
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => confirmDeleteDocument(document)}
                    disabled={isProcessing === document.id || isDeleting === document.id}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">Delete</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the document "{selectedDocument?.name}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteDocument} className="bg-red-600 hover:bg-red-700">
              {isDeleting === selectedDocument?.id ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
