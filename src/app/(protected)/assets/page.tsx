"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import type { Tables } from '@/types/database.types';
import { ASSET_CATEGORIES } from '@/types/asset.types';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { PlusCircle, ArrowLeft, Edit, Trash2, Home, Package, Gem } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from 'sonner';
import AssetForm from '@/components/Assets/AssetForm';
import AssetPieChart from '@/components/Assets/AssetPieChart';
import PageHeading from '@/components/ui/PageHeading';
import { formatCurrency } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog';

export default function AssetsPage() {
  const router = useRouter();
  const [assets, setAssets] = useState<Tables<'assets'>[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Tables<'assets'> | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [filteredAssets, setFilteredAssets] = useState<Tables<'assets'>[]>([]);
  const [selectedCurrency] = useState('USD');
  const currencySymbol = '$';

  const handleEditAsset = (asset: Tables<'assets'>) => {
    setSelectedAsset(asset);
    setIsFormOpen(true);
  };

  const handleDeleteClick = (asset: Tables<'assets'>) => {
    setSelectedAsset(asset);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedAsset) return;

    try {
      const response = await fetch(`/api/assets/${selectedAsset.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete asset');

      setAssets(prev => prev.filter(a => a.id !== selectedAsset.id));
      setFilteredAssets(prev => prev.filter(a => a.id !== selectedAsset.id));
      toast.success('Asset deleted successfully');
    } catch (error) {
      console.error('Error deleting asset:', error);
      toast.error('Failed to delete asset');
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedAsset(null);
    }
  };

  const convertCurrency = (amount: number) => {
    return amount;
  };

  useEffect(() => {
    const fetchAssets = async () => {
      try {
        const response = await fetch('/api/assets');
        if (!response.ok) throw new Error('Failed to fetch assets');
        const data: Tables<'assets'>[] = await response.json();
        setAssets(data);
        setFilteredAssets(data);
      } catch (error) {
        console.error('Error fetching assets:', error);
        toast.error('Failed to load assets');
        const mockAssets: Tables<'assets'>[] = [
          {
            id: '1',
            user_id: '1',
            name: 'Primary Residence',
            type: 'physical' as const,
            category: 'real_estate',
            value: 450000,
            currency: 'USD',
            location: '123 Main St',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            acquisition_date: null,
            image_url: null,
            description: null,
            notes: null,
            account_details: null
          }
        ];
        setAssets(mockAssets);
        setFilteredAssets(mockAssets);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssets();
  }, []);

  useEffect(() => {
    setFilteredAssets(selectedCategory
      ? assets.filter(asset => asset.category === selectedCategory)
      : assets
    );
  }, [selectedCategory, assets]);

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-8">
        <PageHeading
          title="Asset Portfolio"
          description="Manage your valuable assets"
          icon={<Gem className="w-8 h-8 ml-2" />}
        />
        <Button onClick={() => setIsFormOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Asset
        </Button>
      </div>

      <div className="my-8">
        <AssetPieChart
          assets={assets}
          selectedCurrency={selectedCurrency}
          currencySymbol={currencySymbol}
          convertCurrency={convertCurrency}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredAssets.map((asset) => (
          <Card key={asset.id} className="relative">
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold">{asset.name}</h3>
                  <p className="text-muted-foreground">{asset.category}</p>
                </div>
                <div className="flex gap-2">
                  <Button variant="ghost" size="icon" onClick={() => handleEditAsset(asset)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" onClick={() => handleDeleteClick(asset)}>
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
              </div>
              <div className="mt-4 space-y-1">
                <p>{formatCurrency(asset.value || 0, asset.currency || 'USD')}</p>
                {asset.location && <p className="text-sm">{asset.location}</p>}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedAsset ? 'Edit Asset' : 'New Asset'}</DialogTitle>
          </DialogHeader>
          <AssetForm
            defaultValues={selectedAsset as Partial<Tables<'assets'> & { type: 'physical' | 'digital' }>}
            onSubmit={async (data) => {
              try {
                const response = await fetch(
                  selectedAsset ? `/api/assets/${selectedAsset.id}` : '/api/assets',
                  {
                    method: selectedAsset ? 'PUT' : 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                  }
                );
                if (!response.ok) throw new Error('Failed to save asset');
                setIsFormOpen(false);
                router.refresh();
              } catch (error) {
                console.error('Error saving asset:', error);
                toast.error('Failed to save asset');
              }
            }}
            onCancel={() => setIsFormOpen(false)}
          />
        </DialogContent>
      </Dialog>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this asset? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-destructive hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
