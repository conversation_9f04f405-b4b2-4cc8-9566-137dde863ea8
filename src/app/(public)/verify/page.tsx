"use client";

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useAuth } from '@/context/auth-context';
import MainHeader from '@/components/Navigation/MainHeader';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, RefreshCw } from 'lucide-react';
import { supabase } from '@/lib/supabase-unified';

// Import the shared fallback component
import ClientFallback from '@/components/ClientFallback';

// Main component wrapped with Suspense
function VerifyPageContent() {
  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';
  const [otp, setOtp] = useState('');
  const { verifyOtp } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resending, setResending] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (!email) {
      toast.error('Email is required for verification');
      router.push('/register');
    }
  }, [email, router]);

  const handleVerify = async () => {
    if (otp.length !== 6) {
      toast.error('Please enter a valid 6-digit verification code');
      return;
    }

    setIsSubmitting(true);
    try {
      await verifyOtp(email, otp);
      // Redirect handled in AuthContext
    } catch (error) {
      console.error('Verification error:', error);
      // Error handled in AuthContext
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendCode = async () => {
    if (!email) {
      toast.error('Email is required to resend verification code');
      return;
    }

    setResending(true);
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email,
      });

      if (error) throw error;

      toast.success('A new verification code has been sent to your email');
    } catch (error: any) {
      toast.error(error.message || 'Failed to resend verification code');
    } finally {
      setResending(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <MainHeader />
      <div className="flex-1 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Card>
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center">Verify your email</CardTitle>
              <CardDescription className="text-center">
                We've sent a verification code to {email || 'your email'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block">
                  Enter your 6-digit code
                </label>
                <InputOTP maxLength={6} value={otp} onChange={setOtp}>
                  <InputOTPGroup>
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                    <InputOTPSlot index={4} />
                    <InputOTPSlot index={5} />
                  </InputOTPGroup>
                </InputOTP>
              </div>

              <Button
                onClick={handleVerify}
                className="w-full"
                disabled={isSubmitting || otp.length !== 6}
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></span>
                    Verifying...
                  </span>
                ) : (
                  'Verify Email'
                )}
              </Button>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <div className="text-sm text-center w-full">
                <button
                  onClick={handleResendCode}
                  className="inline-flex items-center text-primary hover:underline"
                  disabled={resending}
                >
                  {resending ? (
                    <>
                      <RefreshCw className="mr-1 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="mr-1 h-4 w-4" />
                      Resend code
                    </>
                  )}
                </button>
              </div>
              <div className="text-sm text-center w-full">
                <Link href="/register" className="inline-flex items-center text-gray-600 hover:text-gray-800">
                  <ArrowLeft className="mr-1 h-4 w-4" />
                  Back to registration
                </Link>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}

// Export the page component with Suspense
export default function VerifyPage() {
  return (
    <Suspense fallback={<ClientFallback />}>
      <VerifyPageContent />
    </Suspense>
  );
}
