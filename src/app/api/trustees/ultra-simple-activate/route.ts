import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { supabaseAdmin } from '@/lib/supabase-unified';

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the trustee ID from the request body
    const { trusteeId } = await request.json();

    if (!trusteeId) {
      return NextResponse.json(
        { error: 'Trustee ID is required' },
        { status: 400 }
      );
    }

    console.log(`[ultra-simple-activate] Processing invitation ${trusteeId} for user ${user.id} (${user.email})`);

    // First, check if the trustee record exists
    const { data: trusteeData, error: fetchError } = await supabaseAdmin
      .from('trustees')
      .select('*')
      .eq('id', trusteeId)
      .single();

    if (fetchError || !trusteeData) {
      console.error('[ultra-simple-activate] Error fetching trustee record:', fetchError);
      return NextResponse.json(
        { error: 'Trustee not found' },
        { status: 404 }
      );
    }

    console.log('[ultra-simple-activate] Found trustee record:', {
      id: trusteeData.id,
      email: trusteeData.trustee_email,
      status: trusteeData.status,
      first_name: trusteeData.first_name,
      last_name: trusteeData.last_name
    });

    // If already active and linked to this user, we're done
    if (trusteeData.status === 'active' && trusteeData.trustee_user_id === user.id) {
      console.log('[ultra-simple-activate] Trustee already active for this user');
      return NextResponse.json({
        success: true,
        message: 'Trustee already active',
        alreadyActive: true
      });
    }

    // If linked to another user, return error
    if (trusteeData.trustee_user_id && trusteeData.trustee_user_id !== user.id) {
      console.error('[ultra-simple-activate] Trustee already linked to a different user:', trusteeData.trustee_user_id);
      return NextResponse.json(
        { error: 'This invitation has already been accepted by another user' },
        { status: 403 }
      );
    }

    // Simple direct update - no name changes, just activation
    try {
      const updateData = {
        trustee_user_id: user.id,
        status: 'active',
        invitation_accepted_at: new Date().toISOString(),
      };

      console.log('[ultra-simple-activate] Updating trustee record with data:', updateData);

      const { error: updateError } = await supabaseAdmin
        .from('trustees')
        .update(updateData)
        .eq('id', trusteeId);

      if (updateError) {
        console.error('[ultra-simple-activate] Error updating trustee record:', updateError);
        return NextResponse.json(
          { error: `Failed to update trustee: ${updateError.message}` },
          { status: 500 }
        );
      }

      console.log('[ultra-simple-activate] Successfully activated trustee:', trusteeId);
      
      return NextResponse.json({
        success: true,
        message: 'Trustee activated successfully'
      });
    } catch (error: any) {
      console.error('[ultra-simple-activate] Exception updating trustee record:', error);
      return NextResponse.json(
        { error: `Exception updating trustee: ${error.message || 'Unknown error'}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('[ultra-simple-activate] Unhandled exception in main handler:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
