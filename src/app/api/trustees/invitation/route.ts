import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { supabaseAdmin } from '@/lib/supabase-unified';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';

export async function GET(request: NextRequest) {
  try {
    const inviteId = request.nextUrl.searchParams.get('id');
    let currentUserId = null;

    // Check if the user is already authenticated via session
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (sessionToken) {
      const session = await getSessionByToken(sessionToken);
      if (session) {
        const user = await getUserById(session.user_id);
        if (user) {
          currentUserId = user.id;
        }
      }
    }

    if (!inviteId) {
      return NextResponse.json(
        { error: 'Invitation ID is required' },
        { status: 400 }
      );
    }

    console.log('Processing invitation request for ID:', inviteId, 'Current user ID:', currentUserId);

    // Get the invitation details
    let invitation: any = null;
    let inviteError: any = null;

    try {
      console.log('Fetching invitation with ID:', inviteId);
      const response = await supabaseAdmin
        .from('trustees')
        .select('*')
        .eq('id', inviteId)
        .single();

      invitation = response.data;
      inviteError = response.error;

      console.log('Invitation fetch response:', {
        found: !!invitation,
        error: inviteError ? inviteError.message : 'None'
      });
    } catch (error) {
      console.error('Exception fetching invitation:', error);
      inviteError = { message: 'Failed to fetch invitation' };
    }

    if (inviteError || !invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    // Get the inviter's details
    // First try to get from auth_users_mapping to find the custom user ID
    const { data: mapping } = await supabaseAdmin
      .from('auth_users_mapping')
      .select('custom_user_id')
      .eq('auth_user_id', invitation.user_id)
      .maybeSingle();

    let inviter = null;

    // If we found a mapping, use it to get the user from custom_users
    if (mapping && mapping.custom_user_id) {
      const { data: customUser } = await supabaseAdmin
        .from('custom_users')
        .select('*')
        .eq('id', mapping.custom_user_id)
        .single();

      inviter = customUser;
    }

    // If no mapping or error, try to get from profiles directly
    if (!inviter) {
      const { data: profileUser } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('id', invitation.user_id)
        .single();

      inviter = profileUser;
    }

    // If still no inviter, try to get from auth.users directly
    if (!inviter) {
      try {
        const { data: authUsers } = await supabaseAdmin.auth.admin.listUsers();
        const authUser = authUsers.users.find((u: any) => u.id === invitation.user_id);

        if (authUser) {
          // Create a minimal inviter object from auth user data
          inviter = {
            id: authUser.id,
            email: authUser.email,
            first_name: authUser.user_metadata?.first_name || 'User',
            last_name: authUser.user_metadata?.last_name || '',
          };
        }
      } catch (authError) {
        console.error('Error getting auth user:', authError);
      }
    }

    if (!inviter) {
      console.error('Could not find inviter with ID:', invitation.user_id);
      // Create a fallback inviter object
      inviter = {
        id: invitation.user_id,
        email: '<EMAIL>',
        first_name: 'Legalock',
        last_name: 'User',
      };
    }

    console.log('Returning invitation data:', {
      invitationId: invitation.id,
      inviterName: `${inviter.first_name} ${inviter.last_name}`,
      inviterEmail: inviter.email,
      trusteeEmail: invitation.trustee_email,
      status: invitation.status,
      currentUserId
    });

    return NextResponse.json({
      invitation,
      inviter,
      currentUserId
    });
  } catch (error: any) {
    console.error('Error fetching trustee invitation:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch invitation' },
      { status: 500 }
    );
  }
}
