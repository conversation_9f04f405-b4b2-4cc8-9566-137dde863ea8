import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { supabaseAdmin } from '@/lib/supabase-unified';
import { checkResourceLimit } from '@/middleware/subscription-check';
import { ensureAuthUserMapping } from '@/lib/auth-mapping-utils';

export async function GET(_request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Use the ensureAuthUserMapping utility to get or create the auth user mapping
    console.log(`[trustees/GET] Getting auth user mapping for user ${user.id}`);
    const authUserId = await ensureAuthUserMapping(user.id);

    console.log(`[trustees/GET] Auth user mapping result: ${authUserId}`);
    // Even if authUserId is null, we'll use the user.id as a fallback
    const userIdToUse = authUserId || user.id;

    // Get trustees for the user
    console.log(`[trustees/GET] Fetching trustees for user ID: ${userIdToUse}`);
    const { data, error } = await supabaseAdmin
      .from('trustees')
      .select('*')
      .eq('user_id', userIdToUse)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching trustees:', error);
      return NextResponse.json(
        { error: 'Failed to fetch trustees' },
        { status: 500 }
      );
    }

    // If no trustees found, return an empty array (not an error)
    if (!data) {
      return NextResponse.json([]);
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in trustees API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.first_name || !body.last_name || !body.email || !body.permissions) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Normalize the email to lowercase
    const normalizedEmail = body.email.toLowerCase().trim();

    // Use the ensureAuthUserMapping utility to get or create the auth user mapping
    console.log(`[trustees/POST] Getting auth user mapping for user ${user.id}`);
    const authUserId = await ensureAuthUserMapping(user.id);
    const userIdToUse = authUserId || user.id;

    // Check if the user has reached their trustee limit
    const { hasReachedLimit, currentCount, maxAllowed } = await checkResourceLimit(
      user.id,
      'trustees',
      'trustees'
    );

    if (hasReachedLimit) {
      return NextResponse.json(
        {
          error: 'Trustee limit reached',
          limit: maxAllowed,
          current: currentCount,
          upgrade: true
        },
        { status: 403 }
      );
    }

    // Check if this email is already a trustee for this user
    const { data: existingTrustee } = await supabaseAdmin
      .from('trustees')
      .select('id')
      .eq('user_id', userIdToUse)
      .eq('trustee_email', normalizedEmail)
      .maybeSingle();

    if (existingTrustee) {
      return NextResponse.json(
        { error: 'This person is already added as your trustee' },
        { status: 400 }
      );
    }

    // Check if the trustee email already exists as a user in our system
    let existingAuthUser = null;
    try {
      // First try to get the user from the profiles table, which doesn't require admin access
      const { data: profileData } = await supabaseAdmin
        .from('profiles')
        .select('id, email')
        .eq('email', normalizedEmail)
        .maybeSingle();

      if (profileData) {
        existingAuthUser = { id: profileData.id, email: profileData.email };
        console.log('Found existing user in profiles table:', existingAuthUser);
      } else {
        // If not found in profiles, try the auth.users table with admin API
        try {
          const { data, error } = await supabaseAdmin.auth.admin.getUserByEmail(normalizedEmail);
          if (!error && data && data.user) {
            existingAuthUser = data.user;
            console.log('Found existing user in auth.users table:', existingAuthUser.id);
          }
        } catch (adminError) {
          console.error('Error getting user from auth.admin API:', adminError);
          // Continue with the process even if this fails
        }
      }
    } catch (error) {
      console.error('Error getting user by email:', error);
      // Continue with the process even if this fails
    }

    // Create the trustee data
    const trusteeData = {
      user_id: userIdToUse,
      trustee_email: normalizedEmail,
      trustee_user_id: existingAuthUser ? existingAuthUser.id : null,
      first_name: body.first_name,
      last_name: body.last_name,
      status: 'pending',
      permissions: body.permissions,
      // Store inviter information for better context
      inviter_email: user.email,
      inviter_first_name: user.firstName || user.first_name || '',
      inviter_last_name: user.lastName || user.last_name || '',
      invitation_sent_at: null // Will be set when invitation is sent
    };

    console.log('Creating trustee with data:', {
      ...trusteeData,
      permissions: `[${Array.isArray(trusteeData.permissions) ? trusteeData.permissions.length : 0} items]`
    });

    // Ensure permissions is an array of strings
    let permissionsArray: string[] = [];
    if (Array.isArray(trusteeData.permissions)) {
      permissionsArray = trusteeData.permissions.map(p => String(p));
    } else if (typeof trusteeData.permissions === 'object') {
      permissionsArray = Object.values(trusteeData.permissions).map(p => String(p));
    }

    // Use the database function to safely create the trustee
    // This bypasses RLS and handles permissions properly
    let data: any = null;
    let insertError: any = null;

    try {
      console.log('Using create_trustee_safely function to create trustee');
      const { data: functionResult, error: functionError } = await supabaseAdmin.rpc(
        'create_trustee_safely',
        {
          p_user_id: trusteeData.user_id,
          p_trustee_email: trusteeData.trustee_email,
          p_first_name: trusteeData.first_name,
          p_last_name: trusteeData.last_name,
          p_permissions: permissionsArray,
          p_inviter_email: trusteeData.inviter_email,
          p_inviter_first_name: trusteeData.inviter_first_name,
          p_inviter_last_name: trusteeData.inviter_last_name
        }
      );

      if (functionError) {
        console.error('Error from create_trustee_safely function:', functionError);
        insertError = functionError;
      } else if (!functionResult.success) {
        console.error('Function reported failure:', functionResult);
        insertError = { message: functionResult.error || 'Unknown error from database function' };
      } else {
        // Get the created trustee data
        const { data: trusteeData, error: fetchError } = await supabaseAdmin
          .from('trustees')
          .select('*')
          .eq('id', functionResult.trustee_id)
          .single();

        if (fetchError) {
          console.error('Error fetching created trustee:', fetchError);
          insertError = fetchError;
        } else {
          data = trusteeData;
        }
      }
    } catch (error) {
      console.error('Exception during trustee creation:', error);
      insertError = { message: `Exception during trustee creation: ${error.message || 'Unknown error'}` };
    }

    if (insertError) {
      console.error('Error creating trustee:', insertError);
      return NextResponse.json(
        { error: `Failed to create trustee: ${insertError.message}` },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'No data returned after trustee creation' },
        { status: 500 }
      );
    }

    // Send the invitation email
    try {
      const inviterName = `${trusteeData.inviter_first_name} ${trusteeData.inviter_last_name}`.trim() || user.email?.split('@')[0] || 'User';
      const trusteeName = `${trusteeData.first_name} ${trusteeData.last_name}`.trim();

      const response = await fetch('/api/send-trustee-invitation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inviterName,
          inviterEmail: trusteeData.inviter_email,
          trusteeName,
          trusteeEmail: trusteeData.trustee_email,
          permissions: permissionsArray,
          inviteId: data.id,
          message: body.message || ''
        }),
      });

      if (!response.ok) {
        console.error('Failed to send invitation email:', await response.text());
        // Continue anyway, as the trustee was created successfully
      }
    } catch (emailError) {
      console.error('Error sending invitation email:', emailError);
      // Continue anyway, as the trustee was created successfully
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in trustees API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const { id, ...trusteeData } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Trustee ID is required' },
        { status: 400 }
      );
    }

    // Use the ensureAuthUserMapping utility to get or create the auth user mapping
    console.log(`[trustees/PUT] Getting auth user mapping for user ${user.id}`);
    const authUserId = await ensureAuthUserMapping(user.id);

    console.log(`[trustees/PUT] Auth user mapping result: ${authUserId}`);
    // Even if authUserId is null, we'll use the user.id as a fallback
    const userIdToUse = authUserId || user.id;

    // First, verify that the trustee belongs to the user
    console.log(`[trustees/PUT] Verifying trustee ${id} belongs to user ${userIdToUse}`);
    const { error: fetchError } = await supabaseAdmin
      .from('trustees')
      .select('id')
      .eq('id', id)
      .eq('user_id', userIdToUse)
      .single();

    if (fetchError) {
      console.error('Error fetching trustee:', fetchError);
      return NextResponse.json(
        { error: 'Trustee not found or you do not have permission to update it' },
        { status: 404 }
      );
    }

    // Update the trustee
    console.log(`[trustees/PUT] Updating trustee ${id} for user ${userIdToUse}`);
    const { data, error: updateError } = await supabaseAdmin
      .from('trustees')
      .update({
        ...trusteeData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', userIdToUse)
      .select();

    if (updateError) {
      console.error('Error updating trustee:', updateError);
      return NextResponse.json(
        { error: `Failed to update trustee: ${updateError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error updating trustee:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update trustee' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the trustee ID from the URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Trustee ID is required' },
        { status: 400 }
      );
    }

    // Use the ensureAuthUserMapping utility to get or create the auth user mapping
    console.log(`[trustees/DELETE] Getting auth user mapping for user ${user.id}`);
    const authUserId = await ensureAuthUserMapping(user.id);

    console.log(`[trustees/DELETE] Auth user mapping result: ${authUserId}`);
    // Even if authUserId is null, we'll use the user.id as a fallback
    const userIdToUse = authUserId || user.id;

    // First, verify that the trustee belongs to the user
    console.log(`[trustees/DELETE] Verifying trustee ${id} belongs to user ${userIdToUse}`);
    const { error: fetchError } = await supabaseAdmin
      .from('trustees')
      .select('id')
      .eq('id', id)
      .eq('user_id', userIdToUse)
      .single();

    if (fetchError) {
      console.error('Error fetching trustee:', fetchError);
      return NextResponse.json(
        { error: 'Trustee not found or you do not have permission to delete it' },
        { status: 404 }
      );
    }

    // Delete the trustee
    console.log(`[trustees/DELETE] Deleting trustee ${id} for user ${userIdToUse}`);
    const { error: deleteError } = await supabaseAdmin
      .from('trustees')
      .delete()
      .eq('id', id)
      .eq('user_id', userIdToUse);

    if (deleteError) {
      console.error('Error deleting trustee:', deleteError);
      return NextResponse.json(
        { error: `Failed to delete trustee: ${deleteError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting trustee:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete trustee' },
      { status: 500 }
    );
  }
}
