import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { getSupabaseAdminClient } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Try to create the vault_documents table if it doesn't exist
    try {
      // Check if the table exists
      const { error: checkError } = await supabaseAdmin
        .from('vault_documents')
        .select('id')
        .limit(1);

      // If the table doesn't exist, return an empty array
      // We can't create tables on the fly in this version of Supabase
      if (checkError && checkError.code === '42P01') { // undefined_table
        return NextResponse.json([]);
      }
    } catch (error) {
      console.log('Error creating vault_documents table:', error);
      // Continue anyway, as the table might already exist
    }

    // Get vault documents for the user
    console.log('Fetching documents for user:', user.id);

    // First try vault_documents table
    let { data, error } = await supabaseAdmin
      .from('vault_documents')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    // If no data or error, try documents table as fallback
    if ((!data || data.length === 0) || error) {
      console.log('No documents found in vault_documents or error occurred, trying documents table');
      const documentsResult = await supabaseAdmin
        .from('documents')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (documentsResult.data && documentsResult.data.length > 0) {
        data = documentsResult.data;
        error = documentsResult.error;
      }
    }

    console.log('API query result:', data);
    console.log('API query error:', error);

    if (error) {
      // If the table doesn't exist, return an empty array
      if (error.code === '42P01') { // undefined_table
        console.log('Table does not exist, returning empty array');
        return NextResponse.json([]);
      }

      console.error('Error fetching vault documents:', error);
      return NextResponse.json(
        { error: 'Failed to fetch vault documents' },
        { status: 500 }
      );
    }

    return NextResponse.json(data || []);
  } catch (error: any) {
    console.error('Error in vault documents API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Add the user_id to the document data
    const documentData = {
      ...body,
      user_id: user.id
    };

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Try to create the vault_documents table if it doesn't exist
    try {
      // Check if the table exists
      const { error: checkError } = await supabaseAdmin
        .from('vault_documents')
        .select('id')
        .limit(1);

      // If the table doesn't exist, return an error
      // We can't create tables on the fly in this version of Supabase
      if (checkError && checkError.code === '42P01') { // undefined_table
        return NextResponse.json(
          { error: 'Vault documents table does not exist' },
          { status: 500 }
        );
      }
    } catch (error) {
      console.log('Error creating vault_documents table:', error);
      // Continue anyway, as the table might already exist
    }

    // Insert the document
    const { data, error } = await supabaseAdmin
      .from('vault_documents')
      .insert(documentData)
      .select();

    if (error) {
      console.error('Error creating vault document:', error);
      return NextResponse.json(
        { error: `Failed to create vault document: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(data[0]);
  } catch (error: any) {
    console.error('Error in vault documents API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
