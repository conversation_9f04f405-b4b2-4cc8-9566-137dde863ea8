import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import {
  createSession,
  getUserByEmail,
  markEmailAsVerified,
  verifyCode,
  createUserProfile,
  supabaseAdmin
} from '@/lib/auth-utils-conditional';

// Route segment config
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    const { email, code, trusteeInvitationId } = await request.json();

    console.log('Verifying email with trustee invitation ID:', trusteeInvitationId);

    // Validate input
    if (!email || !code) {
      return NextResponse.json(
        { error: 'Email and verification code are required' },
        { status: 400 }
      );
    }

    // Verify the code
    const isCodeValid = await verifyCode(email, code);
    if (!isCodeValid) {
      return NextResponse.json(
        { error: 'Invalid or expired verification code' },
        { status: 400 }
      );
    }

    // Get the user
    const user = await getUserByEmail(email);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Mark the email as verified
    await markEmailAsVerified(email);

    // Create a profile for the user if it doesn't exist yet
    try {
      const { data: profileData, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id')
        .eq('id', user.id)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it
        await createUserProfile(user.id, user.email, user.first_name, user.last_name);
      }
    } catch (error) {
      console.error('Error checking/creating profile:', error);
      // Continue anyway, as this is not critical
    }

    // Create a session
    const session = await createSession(user.id);

    // Set the session cookie
    const cookieStore = cookies();
    cookieStore.set('session_token', session.sessionToken, {
      expires: session.expiresAt,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      path: '/',
      sameSite: 'lax',
    });

    // Handle trustee invitations during verification
    try {
      // First, check for the specific trustee invitation ID passed from the client
      if (trusteeInvitationId) {
        console.log(`Handling specific trustee invitation: ${trusteeInvitationId}`);

        // Get the trustee record
        const { data: specificTrustee, error: specificError } = await supabaseAdmin
          .from('trustees')
          .select('*')
          .eq('id', trusteeInvitationId)
          .single();

        if (specificError) {
          console.error('Error fetching specific trustee invitation:', specificError);
        } else if (specificTrustee) {
          // Verify the email matches the invitation
          if (specificTrustee.trustee_email.toLowerCase() !== email.toLowerCase()) {
            console.warn(`Email mismatch for trustee invitation: expected ${specificTrustee.trustee_email}, got ${email}`);
            // We'll still proceed, but log the mismatch
          } else {
            console.log(`Email match confirmed for trustee invitation ${trusteeInvitationId}`);
          }

          // Update the trustee record to link it to this user
          const { error: updateError } = await supabaseAdmin
            .from('trustees')
            .update({
              trustee_user_id: user.id,
              status: 'pending_auth', // Keep as pending_auth until terms are accepted
            })
            .eq('id', trusteeInvitationId);

          if (updateError) {
            console.error('Error updating specific trustee record:', updateError);
          } else {
            console.log(`Successfully linked trustee invitation ${trusteeInvitationId} to user ${user.id}`);
          }
        }
      }

      // Also check for any other pending invitations for this email
      console.log(`Checking for other pending trustee invitations for email: ${email}`);
      const { data: otherInvitations, error: otherError } = await supabaseAdmin
        .from('trustees')
        .select('id, status')
        .eq('trustee_email', email.toLowerCase())
        .eq('status', 'pending')
        .is('trustee_user_id', null);

      if (otherError) {
        console.error('Error checking for other pending trustee invitations:', otherError);
      } else if (otherInvitations && otherInvitations.length > 0) {
        console.log(`Found ${otherInvitations.length} other pending trustee invitations for ${email}`);

        // Update all pending invitations to link them to this user
        for (const invitation of otherInvitations) {
          const { error: updateError } = await supabaseAdmin
            .from('trustees')
            .update({
              trustee_user_id: user.id,
              status: 'pending_auth', // Keep as pending_auth until terms are accepted
            })
            .eq('id', invitation.id);

          if (updateError) {
            console.error(`Error updating trustee record ${invitation.id}:`, updateError);
          } else {
            console.log(`Successfully linked trustee invitation ${invitation.id} to user ${user.id}`);
          }
        }
      } else {
        console.log(`No other pending trustee invitations found for ${email}`);
      }
    } catch (error) {
      console.error('Error handling trustee invitations during verification:', error);
      // Continue anyway, as this is not critical
    }

    // Return user data (excluding sensitive information)
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        emailVerified: true,
      },
    });
  } catch (error: any) {
    console.error('Error in verify:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred during verification' },
      { status: 500 }
    );
  }
}
