import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { getSupabaseAdminClient } from '@/lib/supabase';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: 'Time capsule ID is required' },
        { status: 400 }
      );
    }

    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Use the user's ID directly
    const userId = user.id;

    // Get the time capsule
    const { data, error } = await supabaseAdmin
      .from('time_capsules')
      .select('*')
      .eq('id', id)
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No time capsule found
        return NextResponse.json(
          { error: 'Time capsule not found' },
          { status: 404 }
        );
      }
      
      console.error('Error fetching time capsule:', error);
      return NextResponse.json(
        { error: 'Failed to fetch time capsule' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in GET /api/time-capsules/[id]:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
