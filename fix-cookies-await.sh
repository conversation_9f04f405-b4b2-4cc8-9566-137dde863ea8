#!/bin/bash

# Fix all API routes to use await with cookies() function
echo "Fixing cookies() calls in API routes..."

# Find all files with the pattern and replace them
find src/app/api -name "*.ts" -exec sed -i '' 's/const cookieStore = cookies();/const cookieStore = await cookies();/g' {} \;

echo "Fixed all cookies() calls to use await"
echo "Files updated:"
find src/app/api -name "*.ts" -exec grep -l "const cookieStore = await cookies();" {} \;
