-- Trustees Database Cleanup Script

-- 1. Check for and fix inconsistent trustee records
UPDATE trustees
SET status = 'pending'
WHERE status IS NULL;

-- 2. Add missing columns if they don't exist
ALTER TABLE trustees 
ADD COLUMN IF NOT EXISTS inviter_email TEXT,
ADD COLUMN IF NOT EXISTS inviter_first_name TEXT,
ADD COLUMN IF NOT EXISTS inviter_last_name TEXT,
ADD COLUMN IF NOT EXISTS invitation_accepted_at TIMESTAMP WITH TIME ZONE;

-- 3. Ensure permissions is always a valid JSONB array
UPDATE trustees
SET permissions = '[]'::jsonb
WHERE permissions IS NULL OR permissions::text = '{}' OR permissions::text = 'null';

-- 4. Clean up orphaned trustee records (where user_id doesn't exist)
DELETE FROM trustees
WHERE user_id NOT IN (SELECT id FROM auth.users);

-- 5. Clean up expired tokens
DELETE FROM trustee_tokens
WHERE expires_at < NOW();

-- 6. Simplify RLS Policies
-- Drop all existing policies
DROP POLICY IF EXISTS "Users can view their own trustees" ON public.trustees;
DROP POLICY IF EXISTS "Users can insert their own trustees" ON public.trustees;
DROP POLICY IF EXISTS "Users can update their own trustees" ON public.trustees;
DROP POLICY IF EXISTS "Users can delete their own trustees" ON public.trustees;
DROP POLICY IF EXISTS "Trustees can view their invitations" ON public.trustees;
DROP POLICY IF EXISTS "Service role can manage all trustees" ON public.trustees;

-- Create simplified policies
CREATE POLICY "Users can manage their own trustees"
ON public.trustees
USING (auth.uid() = user_id OR auth.uid() = trustee_user_id);

-- Add a policy for the service role to manage all trustees
CREATE POLICY "Service role can manage all trustees"
ON public.trustees
USING (auth.jwt() ->> 'role' = 'service_role');

-- 7. Create a Single Reliable Activation Function
CREATE OR REPLACE FUNCTION activate_trustee(
  p_trustee_id UUID,
  p_user_id UUID
) RETURNS VOID AS $$
BEGIN
  UPDATE trustees
  SET 
    trustee_user_id = p_user_id,
    status = 'active',
    invitation_accepted_at = NOW(),
    updated_at = NOW()
  WHERE id = p_trustee_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Clean Up User Mappings
INSERT INTO auth_users_mapping (custom_user_id, auth_user_id)
SELECT id, id FROM auth.users
WHERE id NOT IN (SELECT auth_user_id FROM auth_users_mapping)
ON CONFLICT DO NOTHING;

-- 9. Implement Database Monitoring
-- Create an audit log table
CREATE TABLE IF NOT EXISTS trustee_operations_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  operation TEXT NOT NULL,
  trustee_id UUID NOT NULL,
  performed_by UUID,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a trigger function to log operations
CREATE OR REPLACE FUNCTION log_trustee_operations()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO trustee_operations_log (operation, trustee_id, performed_by, details)
  VALUES (
    TG_OP,
    CASE WHEN TG_OP = 'DELETE' THEN OLD.id ELSE NEW.id END,
    COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid),
    CASE 
      WHEN TG_OP = 'DELETE' THEN jsonb_build_object('old', row_to_json(OLD)::jsonb)
      WHEN TG_OP = 'INSERT' THEN jsonb_build_object('new', row_to_json(NEW)::jsonb)
      ELSE jsonb_build_object('old', row_to_json(OLD)::jsonb, 'new', row_to_json(NEW)::jsonb)
    END
  );
  RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql;

-- Add the trigger
DROP TRIGGER IF EXISTS trustees_audit_trigger ON trustees;
CREATE TRIGGER trustees_audit_trigger
AFTER INSERT OR UPDATE OR DELETE ON trustees
FOR EACH ROW EXECUTE FUNCTION log_trustee_operations();

-- 10. Fix any duplicate trustee records
WITH duplicates AS (
  SELECT 
    user_id, 
    trustee_email, 
    MIN(id) as keep_id,
    array_agg(id) as all_ids
  FROM trustees
  GROUP BY user_id, trustee_email
  HAVING COUNT(*) > 1
)
DELETE FROM trustees
WHERE id IN (
  SELECT unnest(all_ids) 
  FROM duplicates
  WHERE unnest(all_ids) != keep_id
);

-- 11. Fix any inconsistent status for the same trustee
WITH active_trustees AS (
  SELECT 
    trustee_email, 
    trustee_user_id
  FROM trustees
  WHERE status = 'active' AND trustee_user_id IS NOT NULL
)
UPDATE trustees t
SET 
  status = 'active',
  trustee_user_id = at.trustee_user_id
FROM active_trustees at
WHERE 
  t.trustee_email = at.trustee_email AND
  t.status = 'pending' AND
  at.trustee_user_id IS NOT NULL;
