/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['ccwvtcudztphwwzzgwvg.supabase.co'],
  },
  // Server Actions are available by default in Next.js 14

  // Fix for browser extension attributes warning
  compiler: {
    styledComponents: true,
  },
  // Ignore specific HTML attributes added by browser extensions
  webpack: (config) => {
    config.module.rules.push({
      test: /\.js$/,
      exclude: /node_modules/,
      use: {
        loader: 'babel-loader',
        options: {
          presets: ['next/babel'],
        },
      },
    });
    return config;
  },
  // Keep trailing slashes for consistent URLs
  trailingSlash: true,
  // Disable CSR bailout warnings
  experimental: {
    missingSuspenseWithCSRBailout: false
  },
  // Configure routes that use cookies to be server-rendered
  serverComponentsExternalPackages: ['crypto'],
  // Polyfill Node.js built-in modules for edge runtime
  transpilePackages: ['crypto'],
};

export default nextConfig;
