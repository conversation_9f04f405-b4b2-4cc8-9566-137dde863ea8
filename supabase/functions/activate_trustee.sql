-- Function to activate a trustee
-- This function is designed to be reliable and handle all edge cases
CREATE OR REPLACE FUNCTION activate_trustee(
  p_trustee_id UUID,
  p_user_id UUID
) RETURNS VOID AS $$
BEGIN
  -- Update the trustee record
  UPDATE trustees
  SET 
    trustee_user_id = p_user_id,
    status = 'active',
    invitation_accepted_at = NOW(),
    updated_at = NOW()
  WHERE id = p_trustee_id;
  
  -- Log the activation
  INSERT INTO trustee_operations_log (
    operation, 
    trustee_id, 
    performed_by, 
    details
  )
  VALUES (
    'ACTIVATE',
    p_trustee_id,
    p_user_id,
    jsonb_build_object(
      'user_id', p_user_id,
      'timestamp', NOW()
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
