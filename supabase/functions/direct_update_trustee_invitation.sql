-- Function to update a trustee invitation
-- This function is designed to be reliable and handle all edge cases
CREATE OR REPLACE FUNCTION direct_update_trustee_invitation(
  p_trustee_id UUID,
  p_current_time TIMESTAMP WITH TIME ZONE
) RETURNS VOID AS $$
BEGIN
  -- Update the trustee record
  UPDATE trustees
  SET 
    invitation_sent_at = p_current_time,
    updated_at = p_current_time
  WHERE id = p_trustee_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
