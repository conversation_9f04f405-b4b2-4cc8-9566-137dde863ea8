{"version": "2.0.0", "tasks": [{"label": "Start Supabase MCP Server", "type": "shell", "command": "npx @supabase/mcp-server-supabase@latest --access-token ********************************************", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}, "group": "none"}, {"label": "Test Supabase MCP Connection", "type": "shell", "command": "echo \"SELECT version();\" | npx @supabase/mcp-server-supabase@latest --access-token ********************************************", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}, "group": "test"}, {"label": "Execute SQL with MCP", "type": "shell", "command": "echo \"${input:sqlQuery}\" | npx @supabase/mcp-server-supabase@latest --access-token ********************************************", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}, "group": "none"}, {"label": "Execute SQL File with MCP", "type": "shell", "command": "cat ${input:sqlFile} | npx @supabase/mcp-server-supabase@latest --access-token ********************************************", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}, "group": "none"}], "inputs": [{"id": "sqlQuery", "description": "Enter SQL query to execute:", "default": "SELECT NOW();", "type": "promptString"}, {"id": "sqlFile", "description": "Enter path to SQL file:", "default": "query.sql", "type": "promptString"}]}